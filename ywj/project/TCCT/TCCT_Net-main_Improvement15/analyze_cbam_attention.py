import torch
import numpy as np
import matplotlib.pyplot as plt
from models.feature_fusion import Decision_Fusion
from data.data_loader import get_source_data_inference  # 修正导入
from data.data_processing import batch_cwt
from utilities.Grad_CAM import CBamGradCAM
import json

def load_model_weights(model_path, n_classes=4):
    """加载模型权重（处理DataParallel前缀问题）"""
    model = Decision_Fusion(n_classes)
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 检查权重是否有module前缀
    if any(key.startswith('module.') for key in checkpoint.keys()):
        # 权重有module前缀，需要用DataParallel包装模型
        model = torch.nn.DataParallel(model)
        model.load_state_dict(checkpoint)
    else:
        # 权重没有module前缀，先加载到单GPU模型，再包装
        model.load_state_dict(checkpoint)
        model = torch.nn.DataParallel(model)
    
    model.eval()
    return model

def analyze_cbam_attention():
    """分析CBAM注意力效果"""
    
    # 加载配置
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # 加载模型
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = load_model_weights('model_weights.pth', config['n_classes'])
    model = model.to(device)
    
    # 加载测试数据 - 使用推理函数
    test_signal_data, test_label = get_source_data_inference(
        config['inference_folder_path'], 
        config['label_file_inference'],
        config['behavioral_features'], 
        config['target_mean'], 
        config['target_std']
    )
    
    # 转换为tensor并移动到设备
    test_signal_data = torch.from_numpy(test_signal_data).float().to(device)
    test_label = torch.from_numpy(test_label).long().to(device)
    
    # 选择几个样本进行分析
    sample_indices = [0, 10, 20, 30]  # 选择不同类别的样本
    
    cbam_analyzer = CBamGradCAM(model)
    
    for idx in sample_indices:
        if idx >= len(test_signal_data):
            print(f"跳过样本 {idx}，超出数据范围")
            continue
        
        # 准备输入数据
        ts_input = test_signal_data[idx:idx+1]
        
        # 生成CWT数据
        frequencies = np.linspace(config['freq_min'], config['freq_max'], config['tensor_height'])
        tf_input = batch_cwt(ts_input, frequencies, config['sampling_frequency'])
        
        print(f"\n分析样本 {idx} (真实标签: {test_label[idx].item()})")
        
        # 可视化输入数据
        visualize_input_data(ts_input, tf_input, idx, config)
        
        # TS流注意力分析
        print("分析TS流的CBAM注意力...")
        ts_cams, ts_output = cbam_analyzer.compare_before_after_cbam(
            (ts_input, tf_input), stream_type='TS'
        )
        
        cbam_analyzer.visualize_attention_maps(
            ts_cams, ts_input.shape, f'sample_{idx}_TS'
        )
        
        # TF流注意力分析  
        print("分析TF流的CBAM注意力...")
        tf_cams, tf_output = cbam_analyzer.compare_before_after_cbam(
            (ts_input, tf_input), stream_type='TF'
        )
        
        cbam_analyzer.visualize_attention_maps(
            tf_cams, tf_input.shape, f'sample_{idx}_TF'
        )
        
        # 输出预测结果
        predicted_class = torch.argmax(ts_output, dim=1).item()
        confidence = torch.softmax(ts_output, dim=1).max().item()
        
        engagement_classes = ['Not-Engaged', 'Barely-engaged', 'Engaged', 'Highly-Engaged']
        print(f"预测类别: {engagement_classes[predicted_class]}, 置信度: {confidence:.4f}")

def compare_with_without_cbam():
    """对比有无CBAM的模型效果"""
    
    # 这需要你保存两个版本的模型权重：
    # 1. 带CBAM的模型权重
    # 2. 不带CBAM的模型权重
    
    print("对比分析需要两个模型权重文件:")
    print("1. model_with_cbam.pth - 带CBAM的模型")
    print("2. model_without_cbam.pth - 不带CBAM的模型")
    print("请确保这两个文件存在后再运行此功能")

def visualize_input_data(ts_input, tf_input, sample_idx, config):
    """可视化输入数据以便理解"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 时间序列数据
    ts_data = ts_input.squeeze().cpu().numpy()  # (4, 280)
    feature_names = config['behavioral_features']
    
    for i, feature_name in enumerate(feature_names):
        axes[0, 0].plot(ts_data[i], label=feature_name, alpha=0.7)
    axes[0, 0].set_title(f'样本 {sample_idx} - 时间序列特征')
    axes[0, 0].set_xlabel('时间帧')
    axes[0, 0].set_ylabel('特征值')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 时间序列热力图
    im1 = axes[0, 1].imshow(ts_data, aspect='auto', cmap='viridis')
    axes[0, 1].set_title('时间序列热力图')
    axes[0, 1].set_xlabel('时间帧')
    axes[0, 1].set_ylabel('特征维度')
    axes[0, 1].set_yticks(range(len(feature_names)))
    axes[0, 1].set_yticklabels(feature_names)
    plt.colorbar(im1, ax=axes[0, 1])
    
    # 3. 时频表示
    tf_data = tf_input.squeeze().cpu().numpy()  # (freq, time)
    im2 = axes[1, 0].imshow(tf_data, aspect='auto', cmap='plasma', origin='lower')
    axes[1, 0].set_title('连续小波变换(CWT)表示')
    axes[1, 0].set_xlabel('时间帧')
    axes[1, 0].set_ylabel('频率')
    plt.colorbar(im2, ax=axes[1, 0])
    
    # 4. 频率能量分布
    freq_energy = tf_data.mean(axis=1)  # 沿时间轴平均
    frequencies = np.linspace(config['freq_min'], config['freq_max'], len(freq_energy))
    axes[1, 1].plot(frequencies, freq_energy)
    axes[1, 1].set_title('频率能量分布')
    axes[1, 1].set_xlabel('频率 (Hz)')
    axes[1, 1].set_ylabel('平均能量')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'sample_{sample_idx}_input_data.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f'输入数据可视化已保存到 sample_{sample_idx}_input_data.png')

if __name__ == "__main__":
    print("开始CBAM注意力分析...")
    analyze_cbam_attention()
    print("CBAM注意力分析完成！")
