"""
主脚本用于训练模型并保存结果。

该脚本设置了随机种子以确保结果可复现，使用 `train.py` 中的 `train` 函数训练模型，
并将真实标签和预测标签保存到 CSV 文件中。
"""

import json  # 导入 json 模块，用于加载配置文件
import torch  # 导入 PyTorch 库，用于深度学习任务
import random  # 导入 random 模块，用于设置随机种子
import numpy as np  # 导入 numpy 库，用于数值计算
import pandas as pd  # 导入 pandas 库，用于数据处理和保存结果
from train import train  # 从 train.py 中导入 train 函数，用于训练模型
from utilities.plotting import plot_all_confusion_matrices

def load_config(config_path):
    """
    加载配置文件。

    参数:
        config_path (str): 配置文件的路径。

    返回:
        dict: 包含配置信息的字典。
    """
    with open(config_path, 'r') as f:
        config = json.load(f)  # 读取并解析 JSON 格式的配置文件
    return config


# 设置随机种子以确保结果可复现
seed_n = 42  # 随机种子值
random.seed(seed_n)  # 设置 Python 随机种子
np.random.seed(seed_n)  # 设置 numpy 随机种子
torch.manual_seed(seed_n)  # 设置 PyTorch 随机种子
torch.cuda.manual_seed(seed_n)  # 设置当前 GPU 的随机种子
torch.cuda.manual_seed_all(seed_n)  # 设置所有 GPU 的随机种子
torch.backends.cudnn.deterministic = True  # 确保 CUDA 操作是确定性的
torch.backends.cudnn.benchmark = False  # 禁用 CUDA 的自动优化

    
if __name__ == "__main__":
    # 加载配置文件
    config = load_config('config.json')  # 从 config.json 文件中加载配置

    # 训练模型并获取真实标签和预测标签
    y_true, y_pred = train(
        n_classes=config['n_classes'],  # 分类任务的类别数量
        batch_size=config['batch_size'],  # 批量大小
        b1=config['b1'],  # 优化器参数 b1
        b2=config['b2'],  # 优化器参数 b2
        n_epochs=config['n_epochs'],  # 训练的总轮数
        lr=config['lr'],  # 学习率
        behavioral_features=config['behavioral_features'],  # 使用的行为特征列表
        train_folder_path=config['train_folder_path'],  # 训练数据文件夹路径
        test_folder_path=config['test_folder_path'],  # 测试数据文件夹路径
        label_file=config['label_file'],  # 标签文件路径
        milestones=config['milestones'],  # 学习率调整的里程碑
        gamma=config['gamma'],  # 学习率调整的 gamma 值
        patience=config['patience'],  # 早停的耐心值
        sampling_frequency=config['sampling_frequency'],  # 信号的采样频率
        weight_decay=config['weight_decay'],  # 权重衰减（L2 正则化）
        freq_min=config['freq_min'],  # 连续小波变换（CWT）的最小频率
        freq_max=config['freq_max'],  # 连续小波变换（CWT）的最大频率
        tensor_height=config['tensor_height'],  # CWT 的离散频率数量
        target_mean=config.get('target_mean'),  # 预计算的均值
        target_std=config.get('target_std')  # 预计算的标准差
    )
    
    # 生成混淆矩阵图
    engagement_classes = ['Not-Engaged', 'Barely-engaged', 'Engaged', 'Highly-Engaged']
    plot_all_confusion_matrices(y_true, y_pred, engagement_classes)
    
    # 创建结果DataFrame并保存
    results_df = pd.DataFrame({
        'Actual Label': y_true.cpu().numpy(),
        'Predicted Label': y_pred.cpu().numpy()
    })
    results_df.to_csv('test_results.csv', index=False)
    print('\nResults saved to test_results.csv')  # 打印保存成功的提示信息
