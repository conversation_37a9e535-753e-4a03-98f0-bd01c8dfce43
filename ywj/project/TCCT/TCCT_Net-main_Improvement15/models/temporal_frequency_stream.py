import torch  # 导入 PyTorch 库
from torch import nn  # 导入 PyTorch 的神经网络模块

from torch.backends import cudnn  # 导入 CUDA 后端模块
cudnn.benchmark = False  # 禁用 CUDA 的自动优化，以确保结果可复现
cudnn.deterministic = True  # 设置 CUDA 操作是确定性的

class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=8):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(in_planes, in_planes // ratio),
            nn.ReLU(),
            nn.Linear(in_planes // ratio, in_planes)
        )
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        b, c, _, _ = x.size()
        avg_out = self.mlp(self.avg_pool(x).view(b, c))
        max_out = self.mlp(self.max_pool(x).view(b, c))
        out = avg_out + max_out
        scale = self.sigmoid(out).view(b, c, 1, 1)
        return x * scale


class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x_cat = torch.cat([avg_out, max_out], dim=1)
        scale = self.sigmoid(self.conv(x_cat))
        return x * scale


class CBAM(nn.Module):
    def __init__(self, channels, ratio=8, kernel_size=7):
        super().__init__()
        self.ca = ChannelAttention(channels, ratio)
        self.sa = SpatialAttention(kernel_size)

    def forward(self, x):
        x = self.ca(x)
        x = self.sa(x)
        return x


class HybridAttention(nn.Module):
    """
    混合注意力模块：联合处理尺度（频带）注意力 + 空间（时间）注意力
    更好地捕捉时频联合特征，平衡性能与效率
    使用简化的自注意力机制，无需预定义维度
    """
    def __init__(self):
        super().__init__()
        self.dropout = nn.Dropout(0.1)  # 添加dropout防止过拟合

    def forward(self, x):  # x: [B, Channels, Scales, Time]
        # 先对通道维度进行全局平均池化
        x_pooled = torch.mean(x, dim=1)  # [B, Scales, Time]

        # 频带注意力 - 对时间维度取平均，得到每个频带的重要性
        scale_features = x_pooled.mean(dim=2)  # [B, Scales]
        # 使用softmax归一化生成注意力权重
        scale_weights = torch.softmax(scale_features, dim=1)  # [B, Scales]
        scale_weights = scale_weights.unsqueeze(1).unsqueeze(-1)  # [B, 1, Scales, 1]
        x = x * scale_weights  # 广播到 [B, C, Scales, Time]

        # 时间注意力 - 对频带维度取平均，得到每个时间步的重要性
        x_pooled_updated = torch.mean(x, dim=1)  # 重新计算池化后的特征 [B, Scales, Time]
        time_features = x_pooled_updated.mean(dim=1)  # [B, Time]
        # 使用softmax归一化生成注意力权重
        time_weights = torch.softmax(time_features, dim=1)  # [B, Time]
        time_weights = time_weights.unsqueeze(1).unsqueeze(1)  # [B, 1, 1, Time]
        x = x * time_weights  # 广播到 [B, C, Scales, Time]

        return self.dropout(x)


class TF_ConvModule(nn.Module):
    """
    用于提取时间-频率特征的模块。

    参数:
        output_features (int): 输出向量的特征数量。

    属性:
        shallownet (nn.Sequential): 用于从时间-频率数据中提取初始特征的卷积层，
                                    包含对时间和空间卷积的特定调整。
        global_avg_pool (nn.AdaptiveAvgPool2d): 全局平均池化层，用于将空间维度减少为每个特征图的单个值。
        reduce_dim (nn.Linear): 线性层，用于将特征数量减少到所需的输出大小。
    """

    def __init__(self, output_features=64):
        """
        初始化 TF_ConvModule。

        参数:
            output_features (int): 输出向量的特征数量。
        """
        super(TF_ConvModule, self).__init__()

        # ✅ 保留原有的CBAM注意力模块
        self.cbam = CBAM(32)     # CBAM 注意力模块，输入通道为 32

        # ✅ 新增混合注意力模块，用于时频联合特征处理
        # 使用自适应的混合注意力，无需预定义维度
        self.hybrid_attention = HybridAttention()

        # 类似 shallownet 的架构，用于从时间-频率数据中提取初始特征
        self.shallownet = nn.Sequential(
            nn.Conv2d(4, 16, (1, 10), padding=(0, 2)),  # 2D 卷积层，输入通道为 4，输出通道为 16，卷积核大小为 (1, 10)
            nn.Conv2d(16, 32, (2, 1), padding=(0, 0)),  # 2D 卷积层，输入通道为 16，输出通道为 32，卷积核大小为 (2, 1)
            nn.BatchNorm2d(32),  # 批量归一化层，对 32 个通道进行归一化
            nn.ELU(),  # ELU 激活函数
            nn.AvgPool2d((1, 3), stride=(1, 2)),  # 平均池化层
            nn.Dropout(0.6),  # Dropout 层，丢弃率为 60%
        )

        # 全局平均池化，用于减少空间维度
        self.global_avg_pool = nn.AdaptiveAvgPool2d((1, 1))

        # 降维到所需的特征大小
        self.reduce_dim = nn.Linear(32, output_features)  # 线性层，将 32 维特征映射到 output_features 维

        # 👈 添加用于存储中间激活的属性
        self.intermediate_activations = {}

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数。

        参数:
            x (torch.Tensor): 输入张量，期望为一个批量的 2D 张量，形状为 [batch_size, channels, height, width]。

        返回:
            torch.Tensor: 降维后的输出张量，形状为 [batch_size, output_features]。
        """

        # 保存中间激活以便Grad-CAM分析
        x = self.shallownet[:2](x)  # Conv2d + Conv2d
        self.intermediate_activations['before_attention'] = x.clone()

        # ✅ 先应用CBAM注意力（通道+空间注意力）
        x = self.cbam(x)
        self.intermediate_activations['after_cbam'] = x.clone()

        # ✅ 再应用混合注意力（尺度+时间注意力）
        x = self.hybrid_attention(x)
        self.intermediate_activations['after_hybrid_attention'] = x.clone()

        x = self.shallownet[2:](x)  # BN + ELU + Pooling + Dropout
        x = self.global_avg_pool(x)
        x = torch.flatten(x, 1)
        x = self.reduce_dim(x)

        return x  # 返回输出张量
