import torch  # 导入 PyTorch 库
from torch import nn  # 导入 PyTorch 的神经网络模块

from torch.backends import cudnn  # 导入 CUDA 后端模块
cudnn.benchmark = False  # 禁用 CUDA 的自动优化，以确保结果可复现
cudnn.deterministic = True  # 设置 CUDA 操作是确定性的

class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=8):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(in_planes, in_planes // ratio),
            nn.ReLU(),
            nn.Linear(in_planes // ratio, in_planes)
        )
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        b, c, _, _ = x.size()
        avg_out = self.mlp(self.avg_pool(x).view(b, c))
        max_out = self.mlp(self.max_pool(x).view(b, c))
        out = avg_out + max_out
        scale = self.sigmoid(out).view(b, c, 1, 1)
        return x * scale


class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x_cat = torch.cat([avg_out, max_out], dim=1)
        scale = self.sigmoid(self.conv(x_cat))
        return x * scale


class CBAM(nn.Module):
    def __init__(self, channels, ratio=8, kernel_size=7):
        super().__init__()
        self.ca = ChannelAttention(channels, ratio)
        self.sa = SpatialAttention(kernel_size)

    def forward(self, x):
        x = self.ca(x)
        x = self.sa(x)
        return x


class TF_ConvModule(nn.Module):
    """
    用于提取时间-频率特征的模块。

    参数:
        output_features (int): 输出向量的特征数量。

    属性:
        shallownet (nn.Sequential): 用于从时间-频率数据中提取初始特征的卷积层，
                                    包含对时间和空间卷积的特定调整。
        global_avg_pool (nn.AdaptiveAvgPool2d): 全局平均池化层，用于将空间维度减少为每个特征图的单个值。
        reduce_dim (nn.Linear): 线性层，用于将特征数量减少到所需的输出大小。
    """

    def __init__(self, output_features=64):
        """
        初始化 TF_ConvModule。

        参数:
            output_features (int): 输出向量的特征数量。
        """
        super(TF_ConvModule, self).__init__()
        self.cbam = CBAM(32)     # ✅ 加入 CBAM 注意力模块，输入通道为 32


        # 类似 shallownet 的架构，用于从时间-频率数据中提取初始特征
        self.shallownet = nn.Sequential(
            nn.Conv2d(4, 16, (1, 10), padding=(0, 2)),  # 2D 卷积层，输入通道为 2，输出通道为 16，卷积核大小为 (1, 10)  #此处四个特征将输入通道改为4
            nn.Conv2d(16, 32, (2, 1), padding=(0, 0)),  # 2D 卷积层，输入通道为 16，输出通道为 32，卷积核大小为 (2, 1)
            nn.BatchNorm2d(32),  # 批量归一化层，对 32 个通道进行归一化
            nn.ELU(),  # ELU 激活函数
            nn.AvgPool2d((1, 3), stride=(1, 2)),  # ✅ 改成合法的小窗口
            # nn.AvgPool2d((1, 20), stride=(1, 15)),  # 平均池化层，用于时间切片
            nn.Dropout(0.6),  # Dropout 层，丢弃率为 60%
        )
        
        # 全局平均池化，用于减少空间维度
        self.global_avg_pool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 降维到所需的特征大小
        self.reduce_dim = nn.Linear(32, output_features)  # 线性层，将 32 维特征映射到 output_features 维
        
        # 👈 添加用于存储中间激活的属性
        self.intermediate_activations = {}

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数。

        参数:
            x (torch.Tensor): 输入张量，期望为一个批量的 2D 张量，形状为 [batch_size, channels, height, width]。

        返回:
            torch.Tensor: 降维后的输出张量，形状为 [batch_size, output_features]。
        """

        # 保存中间激活以便Grad-CAM分析
        x = self.shallownet[:2](x)  # Conv2d + Conv2d
        self.intermediate_activations['before_cbam'] = x.clone()
        
        x = self.cbam(x)  # CBAM注意力
        self.intermediate_activations['after_cbam'] = x.clone()
        
        x = self.shallownet[2:](x)  # BN + ELU + Pooling + Dropout
        x = self.global_avg_pool(x)
        x = torch.flatten(x, 1)
        x = self.reduce_dim(x)
        
        return x  # 返回输出张量
