import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import cv2
from typing import List, Tuple

class GradCAM:
    def __init__(self, model, target_layers: List[str]):
        self.model = model
        self.target_layers = target_layers
        self.gradients = {}
        self.activations = {}
        self.hooks = []
        self._register_hooks()
    
    def _register_hooks(self):
        def forward_hook(name):
            def hook(module, input, output):
                self.activations[name] = output.detach()
            return hook
        
        def backward_hook(name):
            def hook(module, grad_input, grad_output):
                self.gradients[name] = grad_output[0].detach()
            return hook
        
        for name, module in self.model.named_modules():
            if name in self.target_layers:
                self.hooks.append(module.register_forward_hook(forward_hook(name)))
                self.hooks.append(module.register_backward_hook(backward_hook(name)))
    
    def generate_cam(self, input_data, class_idx=None):
        # 处理输入数据
        if isinstance(input_data, tuple):
            # 如果是元组，说明是(TS_input, TF_input)
            ts_input, tf_input = input_data
            output = self.model(ts_input, tf_input)
        else:
            # 如果是单个tensor，需要生成对应的TF输入
            ts_input = input_data
            # 这里需要生成TF输入，但这种情况下应该避免
            raise ValueError("需要提供完整的(TS_input, TF_input)输入数据")
        
        if class_idx is None:
            class_idx = output.argmax(dim=1)
        
        # 反向传播
        self.model.zero_grad()
        class_score = output[:, class_idx].sum()
        class_score.backward()
        
        cams = {}
        for layer_name in self.target_layers:
            if layer_name in self.gradients and layer_name in self.activations:
                gradients = self.gradients[layer_name]
                activations = self.activations[layer_name]
                
                # 计算权重 (全局平均池化)
                weights = torch.mean(gradients, dim=(2, 3), keepdim=True)
                
                # 加权求和
                cam = torch.sum(weights * activations, dim=1, keepdim=True)
                cam = F.relu(cam)
                
                # 归一化到[0,1]
                cam = cam - cam.min()
                cam = cam / (cam.max() + 1e-8)
                
                cams[layer_name] = cam
        
        return cams, output
    
    def cleanup(self):
        for hook in self.hooks:
            hook.remove()

class CBamGradCAM:
    """专门用于CBAM模块的Grad-CAM可视化"""
    
    def __init__(self, model):
        self.model = model
        
    def compare_before_after_cbam(self, input_data, stream_type='TS'):
        """对比CBAM前后的特征激活"""
        
        if stream_type == 'TS':
            # TS流的目标层
            target_layers = [
                'module.TS_Stream.TS_ConvModule.shallownet.1',  # Conv2d层 (CBAM前)
                'module.TS_Stream.TS_ConvModule.cbam.ca',       # 通道注意力
                'module.TS_Stream.TS_ConvModule.cbam.sa'        # 空间注意力
            ]
        elif stream_type == 'TF':
            # TF流的目标层
            target_layers = [
                'module.TF_ConvModule.shallownet.1',  # Conv2d层 (CBAM前)  
                'module.TF_ConvModule.cbam.ca',       # 通道注意力
                'module.TF_ConvModule.cbam.sa'        # 空间注意力
            ]
        
        grad_cam = GradCAM(self.model, target_layers)
        
        try:
            # 传入完整的输入数据元组
            ts_input, tf_input = input_data
            cams, output = grad_cam.generate_cam((ts_input, tf_input))
            
            return cams, output
        finally:
            grad_cam.cleanup()
    
    def visualize_attention_maps(self, cams, input_shape, save_prefix):
        """可视化注意力图并添加详细说明"""
        
        if not cams:
            print("没有生成注意力图")
            return
        
        fig, axes = plt.subplots(2, len(cams), figsize=(4*len(cams), 8))
        if len(cams) == 1:
            axes = axes.reshape(2, 1)
        
        for idx, (layer_name, cam) in enumerate(cams.items()):
            # 转换为numpy并移除batch维度
            cam_np = cam.squeeze().cpu().numpy()
            
            # 上排：原始注意力图
            im1 = axes[0, idx].imshow(cam_np, cmap='jet', aspect='auto')
            axes[0, idx].set_title(f'{layer_name}\n(原始注意力)', fontsize=10)
            axes[0, idx].set_xlabel('时间帧' if 'TS' in save_prefix else '时间')
            axes[0, idx].set_ylabel('特征维度' if 'TS' in save_prefix else '频率')
            plt.colorbar(im1, ax=axes[0, idx])
            
            # 下排：归一化后的注意力图
            cam_norm = (cam_np - cam_np.min()) / (cam_np.max() - cam_np.min() + 1e-8)
            im2 = axes[1, idx].imshow(cam_norm, cmap='hot', aspect='auto')
            axes[1, idx].set_title(f'{layer_name}\n(归一化注意力)', fontsize=10)
            axes[1, idx].set_xlabel('时间帧' if 'TS' in save_prefix else '时间')
            axes[1, idx].set_ylabel('特征维度' if 'TS' in save_prefix else '频率')
            plt.colorbar(im2, ax=axes[1, idx])
            
            # 添加数值信息
            print(f"\n{layer_name} 注意力统计:")
            print(f"  形状: {cam_np.shape}")
            print(f"  最大值: {cam_np.max():.4f}")
            print(f"  最小值: {cam_np.min():.4f}")
            print(f"  平均值: {cam_np.mean():.4f}")
        
        plt.tight_layout()
        plt.savefig(f'{save_prefix}_attention_maps.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f'注意力图已保存到 {save_prefix}_attention_maps.png')

def analyze_intermediate_activations(model, ts_input, tf_input):
    """直接分析模型中间激活（不依赖hook）"""
    
    model.eval()
    with torch.no_grad():
        # 前向传播获取输出
        output = model(ts_input, tf_input)
        
        # 获取TS流的中间激活
        ts_activations = model.module.TS_Stream.TS_ConvModule.intermediate_activations
        
        # 获取TF流的中间激活
        tf_activations = model.module.TF_ConvModule.intermediate_activations
        
        return {
            'TS_before_cbam': ts_activations.get('before_cbam'),
            'TS_after_cbam': ts_activations.get('after_cbam'),
            'TF_before_cbam': tf_activations.get('before_cbam'),
            'TF_after_cbam': tf_activations.get('after_cbam')
        }, output

def visualize_activation_comparison(activations, save_prefix='activation_comparison'):
    """可视化CBAM前后的激活对比"""
    
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    streams = ['TS', 'TF']
    stages = ['before_cbam', 'after_cbam']
    
    for i, stream in enumerate(streams):
        for j, stage in enumerate(stages):
            key = f'{stream}_{stage}'
            if key in activations and activations[key] is not None:
                # 取第一个样本的第一个通道
                activation = activations[key][0, 0].cpu().numpy()
                
                im = axes[i, j*2].imshow(activation, cmap='viridis')
                axes[i, j*2].set_title(f'{stream} {stage}')
                axes[i, j*2].axis('off')
                plt.colorbar(im, ax=axes[i, j*2])
                
                # 绘制激活强度分布
                axes[i, j*2+1].hist(activation.flatten(), bins=50, alpha=0.7)
                axes[i, j*2+1].set_title(f'{stream} {stage} Distribution')
                axes[i, j*2+1].set_xlabel('Activation Value')
                axes[i, j*2+1].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig(f'{save_prefix}.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f'Activation comparison saved to {save_prefix}.png')
