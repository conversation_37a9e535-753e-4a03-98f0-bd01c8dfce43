import os
import pandas as pd
import numpy as np
from tqdm import tqdm
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score

def global_kmeans_filter(folder_path, behavioral_features, n_clusters="auto", distance_threshold=3.0, output_csv="global_kmeans_outliers.csv"):
    """
    对所有样本做全局聚类，筛选距离聚类中心过远的异常样本。

    参数:
        folder_path (str): 包含 CSV 文件的文件夹路径。
        behavioral_features (list): 要提取的行为特征列。
        n_clusters (int): KMeans 聚类簇数。
        distance_threshold (float): 距离超过该标准差倍数将视为异常。
        output_csv (str): 输出的异常文件列表 CSV。
    """
    all_vectors = []
    filenames = []

    for fname in tqdm(os.listdir(folder_path), desc=f"提取 {os.path.basename(folder_path)} 特征"):
        if not fname.endswith(".csv"):
            continue
        csv_path = os.path.join(folder_path, fname)
        try:
            df = pd.read_csv(csv_path)[behavioral_features]
            df = df.replace([np.inf, -np.inf], np.nan).dropna()
            if df.shape[0] == 0:
                continue
            sample_vector = df.values.flatten()
            all_vectors.append(sample_vector)
            filenames.append(fname)
        except Exception as e:
            print(f"⚠️ 读取失败: {fname}, 原因: {e}")

    if not all_vectors:
        print(f"❌ 没有可用于聚类的样本：{folder_path}")
        return

    # 保证所有向量长度一致
    min_len = min(len(v) for v in all_vectors)
    all_vectors = [v[:min_len] for v in all_vectors]
    X = np.stack(all_vectors)

    # 标准化
    X = StandardScaler().fit_transform(X)

    # --- 自动选择最优聚类数 ---
    if n_clusters == "auto":
        silhouette_scores = []
        candidate_k = range(2, 10)
        for k in candidate_k:
            kmeans_k = KMeans(n_clusters=k, random_state=0).fit(X)
            score = silhouette_score(X, kmeans_k.labels_)
            silhouette_scores.append(score)
        best_k = candidate_k[np.argmax(silhouette_scores)]
        print(f"📌 使用轮廓系数法选择的最优聚类数: {best_k}")
    else:
        best_k = n_clusters

    # --- 执行最终聚类 ---
    kmeans = KMeans(n_clusters=best_k, random_state=0).fit(X)
    centers = kmeans.cluster_centers_
    labels = kmeans.labels_

    distances = np.linalg.norm(X - centers[labels], axis=1)
    avg_dist = distances.mean()
    std_dist = distances.std()
    threshold = avg_dist + distance_threshold * std_dist

    # 识别异常文件
    outlier_files = [f for f, d in zip(filenames, distances) if d > threshold]
    print(f"🔍 {folder_path} 异常文件数: {len(outlier_files)} / {len(filenames)}")

    pd.DataFrame({"文件": outlier_files}).to_csv(output_csv, index=False)
    print(f"✅ 异常文件保存至: {output_csv}")

if __name__ == "__main__":
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]  # 根据需要修改特征名

    # 训练集异常检测
    train_path = "/data_nas/dataset/Learning_status_detection/EngageNet_Processed_CSV/Train"
    global_kmeans_filter(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters=4,
        distance_threshold=2.5,
        output_csv="train_kmeans_outliers.csv"
    )

    # 验证集异常检测
    val_path = "/data_nas/dataset/Learning_status_detection/EngageNet_Processed_CSV/Validation"
    global_kmeans_filter(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters=4,
        distance_threshold=2.5,
        output_csv="val_kmeans_outliers.csv"
    )
