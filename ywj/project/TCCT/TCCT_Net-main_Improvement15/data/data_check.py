import os
import pandas as pd
import numpy as np
from tqdm import tqdm

def check_feature_statistics(csv_path, behavioral_features, missing_threshold=0.00, max_reasonable_value=10):
    """
    检查单个CSV文件的指定行为特征列中的异常值、缺失值、inf值、极端值。
    """
    try:
        df = pd.read_csv(csv_path)
    except Exception as e:
        return [{"文件": os.path.basename(csv_path), "特征": "加载失败", "异常情况": str(e)}]

    df = df[behavioral_features]
    results = []

    for col in behavioral_features:
        if col not in df.columns:
            results.append({
                "文件": os.path.basename(csv_path),
                "特征": col,
                "缺失值数": "列缺失",
                "inf 数": "",
                "最小值": "",
                "最大值": "",
                "异常情况": "缺少该列"
            })
            continue

        data = df[col]
        n_total = len(data)
        n_nan = data.isna().sum()
        n_inf = np.isinf(data).sum()
        min_val = data.min()
        max_val = data.max()

        issues = []
        if n_nan / n_total > missing_threshold:
            issues.append(f"缺失值比例高 {n_nan/n_total:.2%}")
        if np.abs(max_val) > max_reasonable_value or np.abs(min_val) > max_reasonable_value:
            issues.append("值范围异常")
        if n_inf > 0:
            issues.append("包含 inf")

        # ✅ 仅记录有异常的列
        if issues:
            results.append({
                "文件": os.path.basename(csv_path),
                "特征": col,
                "缺失值数": int(n_nan),
                "inf 数": int(n_inf),
                "最小值": float(min_val) if pd.notna(min_val) else None,
                "最大值": float(max_val) if pd.notna(max_val) else None,
                "异常情况": "; ".join(issues)
            })

    return results


def scan_directory_for_anomalies(folder_path, behavioral_features):
    """
    扫描整个目录，检查每个CSV文件中所有行为特征的异常情况，并添加进度条。
    """
    all_results = []
    csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]

    for file in tqdm(csv_files, desc="正在检查 CSV 文件"):
        csv_path = os.path.join(folder_path, file)
        file_results = check_feature_statistics(csv_path, behavioral_features)
        if file_results:  # ✅ 只保留含异常的结果
            all_results.extend(file_results)

    return pd.DataFrame(all_results)

if __name__ == "__main__":
    # behavioral_features = ["pose_Tx", "pose_Ty"]  # 你要检测的行为特征列
    behavioral_features = ["AU06_r", "AU07_r"]  # 你要检测的行为特征列
    folder_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"

    report_df = scan_directory_for_anomalies(folder_path, behavioral_features)

    if not report_df.empty:
        report_df.to_csv("feature_anomaly_report.csv", index=False)
        print("✔ 异常报告已保存为 feature_anomaly_report.csv")
    else:
        print("✅ 所有文件特征值正常，无异常记录。")
