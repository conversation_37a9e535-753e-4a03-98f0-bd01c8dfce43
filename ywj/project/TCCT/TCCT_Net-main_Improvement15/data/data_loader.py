import os  # 导入 os 模块，用于处理文件路径和目录
import numpy as np  # 导入 numpy 库，用于数值计算
import pandas as pd  # 导入 pandas 库，用于数据处理
from tqdm import tqdm  # 导入 tqdm 库，用于显示进度条
import json

def load_csv_data(folder_path, label_file, behavioral_features, exclude_list_path=None):
    """
    从文件夹中的 CSV 文件加载数据及其对应的标签。

    参数:
        folder_path (str): 包含 CSV 文件的文件夹路径。
        label_file (str): 包含标签的 CSV 文件路径。
        behavioral_features (list of str): 需要从数据中提取的行为特征列表。

    返回:
        np.array: 从 CSV 文件中提取的数据数组。
        np.array: 与数据对应的标签数组。
    """

    # 标签映射
    label_mapping = {
        "Not-Engaged": 0,
        "Barely-engaged": 1,
        "Engaged": 2,
        "Highly-Engaged": 3
    }
    #新加的异常数据判断
    exclude_set = set()
    if exclude_list_path:
        if os.path.exists(exclude_list_path):
            exclude_df = pd.read_csv(exclude_list_path)
            exclude_set = set(exclude_df["文件"].values)
            print(f"🛑 共有 {len(exclude_set)} 个文件将被排除")
        else:
            print(f"⚠️ 排除文件路径不存在，跳过排除逻辑: {exclude_list_path}")



    labels_df = pd.read_csv(label_file)  # 读取标签文件
    all_data, all_labels = [], []  # 初始化存储数据和标签的列表

    # 遍历文件夹中的每个 CSV 文件
    for filename in tqdm(os.listdir(folder_path), desc="Loading data"):
        if filename.endswith('.csv'):  # 检查文件是否为 CSV 文件
             # ✅ 新增：跳过异常数据
            if filename in exclude_set:
                continue

            subject_id = filename.split('.')[0]  # 提取文件名中的主题 ID
            subject_file = os.path.join(folder_path, filename)  # 构建完整的文件路径
            subject_data = pd.read_csv(subject_file)  # 读取 CSV 文件中的数据

            # 提取选定的行为特征并堆叠数据
            subject_data_values = np.stack([subject_data[col].values for col in behavioral_features], axis=0)
            subject_label = labels_df[labels_df['chunk'].str.contains(subject_id)]['label'].values  # 提取对应的标签

            # 如果标签存在，则将数据和标签添加到列表中
            if len(subject_label) > 0:
                label_str = subject_label[0]
                if label_str == "SNP":
                    continue  # 跳过 Subject Not Present 标签
                if label_str in label_mapping:
                     ##### ✅ 在此处插入 NaN 检查和日志写入
                    if np.isnan(subject_data_values).any():
                        with open("nan_subjects_log.txt", "a") as log_file:
                            log_file.write(f"NaN detected in subject: {subject_id}, label: {label_str}\n")

                    all_data.append(subject_data_values)
                    all_labels.append(label_mapping[label_str])
                else:
                    print(f"Unrecognized label '{label_str}' for subject {subject_id}")
            else:
                print(f"No label found for subject {subject_id}")  # 如果没有找到标签，则打印警告

    # 将收集的数据和标签转换为 numpy 数组
    all_data = np.array(all_data)
    all_data = np.expand_dims(all_data, axis=1)  # 扩展数据维度
    all_labels = np.array(all_labels)

    return all_data, all_labels  # 返回数据和标签
 

def get_source_data(train_folder_path, test_folder_path, label_file, behavioral_features):
    """
    从指定文件夹加载并预处理训练和测试数据。

    参数:
        train_folder_path (str): 包含训练数据的文件夹路径。
        test_folder_path (str): 包含测试数据的文件夹路径。
        label_file (str): 包含标签的 CSV 文件路径。
        behavioral_features (list of str): 需要从数据中提取的行为特征列表。

    返回:
        np.array: 处理后的训练数据。
        np.array: 训练数据的标签。
        np.array: 处理后的测试数据。
        np.array: 测试数据的标签。
    """

    # 加载训练数据0
    print('\nLoading train data ...')
    train_data, train_labels = load_csv_data(train_folder_path, label_file, behavioral_features, exclude_list_path="/data_nas/ywj/project/TCCT/TCCT_Net-main_Improvement15/data/train_kmeans_outliers.csv")
    train_labels = train_labels.reshape(1, -1)  # 调整标签形状


    # 打乱训练数据
    shuffle_index = np.random.permutation(len(train_data))
    train_data = train_data[shuffle_index, :, :, :]
    train_labels = train_labels[0][shuffle_index]

    # 加载测试数据
    print('\nLoading test data ...')
    test_data, test_labels = load_csv_data(test_folder_path, label_file, behavioral_features, exclude_list_path="/data_nas/ywj/project/TCCT/TCCT_Net-main_Improvement15/data/val_kmeans_outliers.csv")
    test_labels = test_labels.reshape(-1)  # 调整标签形状

    ###################清洗训练数据中的非法值（NaN 或 Inf），替换为 0.0
    print(f"🔍 数据清洗前 - 训练数据NaN数量: {np.isnan(train_data).sum()}")
    print(f"🔍 数据清洗前 - 测试数据NaN数量: {np.isnan(test_data).sum()}")

    train_data = np.nan_to_num(train_data, nan=0.0, posinf=0.0, neginf=0.0)
    test_data = np.nan_to_num(test_data, nan=0.0, posinf=0.0, neginf=0.0)

    print(f"✅ 数据清洗后 - 训练数据NaN数量: {np.isnan(train_data).sum()}")
    print(f"✅ 数据清洗后 - 测试数据NaN数量: {np.isnan(test_data).sum()}")
    ###################清洗测试数据中的非法值
    
    # === 新增保存CSV的代码 ===
    # 将四维数据展平为二维 (样本数, 特征*时间步)
    samples, channels, features, time_steps = train_data.shape
    flattened_data = train_data.reshape(samples, -1)  # 形状变为 (7415, 1 * 2 * 280) = (7415, 560)

    # 生成列名（包含特征和时间步信息）
    column_names = []
    for t in range(time_steps):
        for feature in behavioral_features:
            column_names.append(f"{feature}_t{t+1}")  # 例如: gaze_0_x_t1, AU01_r_t1,...gaze_0_x_t280

    # 创建DataFrame并添加标签
    df_train = pd.DataFrame(flattened_data, columns=column_names)
    df_train["label"] = train_labels  # 添加标签列

    # 保存到CSV（文件大小约7415行×561列）
    df_train.to_csv("train_data_processed.csv", index=False, float_format="%.4f")  # 保留4位小数
    print(f"\nSaved train_data to train_data_processed.csv (shape: {df_train.shape})")
    # === 新增代码结束 ===

    # 使用训练数据的统计量标准化训练和测试数据
    target_mean = np.mean(train_data)
    target_std = np.std(train_data)

    # 确保标准差不为0，避免除零错误
    if target_std == 0 or np.isnan(target_std):
        print("⚠️  警告：标准差为0或NaN，使用默认值1.0")
        target_std = 1.0

    if np.isnan(target_mean):
        print("⚠️  警告：均值为NaN，使用默认值0.0")
        target_mean = 0.0

    # 保存标准化前的样本为 JSON
    with open("train_data_sample.json", "w") as f:
        json.dump(train_data[:5].tolist(), f)  # 只保存前5个样本

    print("&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&")
    print("train_data shape:", train_data.shape)
    print("train_labels shape:", train_labels.shape)
    print("target_mean:", target_mean)
    print("target_std:", target_std)

    # 执行标准化
    train_data = (train_data - target_mean) / target_std
    test_data = (test_data - target_mean) / target_std

    # 再次检查标准化后的数据
    print(f"📊 标准化后 - 训练数据NaN数量: {np.isnan(train_data).sum()}")
    print(f"📊 标准化后 - 测试数据NaN数量: {np.isnan(test_data).sum()}")


    print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
    print("test_data shape:", test_data.shape)
    print("test_labels shape:", test_labels.shape)



    return train_data, train_labels, test_data, test_labels  # 返回处理后的数据和标签


def get_source_data_inference(inference_folder_path, label_file_inference,
                              behavioral_features, target_mean, target_std):
    """
    从指定文件夹加载并预处理推理数据。

    参数:
        inference_folder_path (str): 包含推理数据的文件夹路径。
        label_file_inference (str): 包含标签的 CSV 文件路径。
        behavioral_features (list of str): 需要从数据中提取的行为特征列表。
        target_mean (float): 用于标准化的训练数据的均值。
        target_std (float): 用于标准化的训练数据的标准差。

    返回:
        np.array: 处理后的推理数据。
        np.array: 推理数据的标签。
    """

    # 加载推理数据
    print('\nLoading data for inference ...')
    inference_data, inference_labels = load_csv_data(inference_folder_path, label_file_inference, behavioral_features)
    inference_labels = inference_labels.reshape(-1)  # 调整标签形状

    # 使用提供的训练数据统计量标准化推理数据
    inference_data = (inference_data - target_mean) / target_std

    return inference_data, inference_labels  # 返回处理后的推理数据和标签