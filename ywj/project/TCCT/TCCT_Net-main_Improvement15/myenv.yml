name: python3.10
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - alsa-lib=1.2.14=hb9d3cd8_0
  - aom=3.9.1=hac33072_0
  - attr=2.5.1=h166bdaf_1
  - bzip2=1.0.8=h4bc722e_7
  - ca-certificates=2025.4.26=hbd8a1cb_0
  - cairo=1.18.4=h3394656_0
  - certifi=2025.4.26=pyhd8ed1ab_0
  - dav1d=1.2.1=hd590300_0
  - dbus=1.13.6=h5008d03_3
  - expat=2.7.0=h5888daf_0
  - ffmpeg=7.1.1=gpl_h127656b_906
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_3
  - fontconfig=2.15.0=h7e30c49_1
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - freetype=2.13.3=ha770c72_1
  - fribidi=1.0.10=h36c2ea0_0
  - gdk-pixbuf=2.42.12=hb9ae30d_0
  - gettext=0.24.1=h5888daf_0
  - gettext-tools=0.24.1=h5888daf_0
  - gmp=6.3.0=hac33072_2
  - graphite2=1.3.13=h59595ed_1003
  - harfbuzz=11.1.0=h3beb420_0
  - icu=75.1=he02047a_0
  - lame=3.100=h166bdaf_1003
  - ld_impl_linux-64=2.40=hf3520f5_7
  - lerc=4.0.0=h0aef613_1
  - level-zero=1.22.0=h84d6215_0
  - libabseil=20250127.1=cxx17_hbbce691_0
  - libasprintf=0.24.1=h8e693c7_0
  - libasprintf-devel=0.24.1=h8e693c7_0
  - libass=0.17.3=h52826cd_2
  - libcap=2.75=h39aace5_0
  - libdeflate=1.23=h86f0d12_0
  - libdrm=2.4.124=hb9d3cd8_0
  - libegl=1.7.0=ha4b6fd6_2
  - libexpat=2.7.0=h5888daf_0
  - libffi=3.4.6=h2dba641_1
  - libflac=1.4.3=h59595ed_0
  - libfreetype=2.13.3=ha770c72_1
  - libfreetype6=2.13.3=h48d6fc4_1
  - libgcc=15.1.0=h767d61c_2
  - libgcc-ng=15.1.0=h69a702a_2
  - libgcrypt-lib=1.11.0=hb9d3cd8_2
  - libgettextpo=0.24.1=h5888daf_0
  - libgettextpo-devel=0.24.1=h5888daf_0
  - libgl=1.7.0=ha4b6fd6_2
  - libglib=2.84.1=h3618099_1
  - libglvnd=1.7.0=ha4b6fd6_2
  - libglx=1.7.0=ha4b6fd6_2
  - libgomp=15.1.0=h767d61c_2
  - libgpg-error=1.55=h3f2d84a_0
  - libhwloc=2.11.2=default_h0d58e46_1001
  - libiconv=1.18=h4ce23a2_1
  - libjpeg-turbo=3.1.0=hb9d3cd8_0
  - liblzma=5.8.1=hb9d3cd8_2
  - liblzma-devel=5.8.1=hb9d3cd8_2
  - libnsl=2.0.1=hd590300_0
  - libogg=1.3.5=hd0c01bc_1
  - libopenvino=2025.0.0=hdc3f47d_3
  - libopenvino-auto-batch-plugin=2025.0.0=h4d9b6c2_3
  - libopenvino-auto-plugin=2025.0.0=h4d9b6c2_3
  - libopenvino-hetero-plugin=2025.0.0=h981d57b_3
  - libopenvino-intel-cpu-plugin=2025.0.0=hdc3f47d_3
  - libopenvino-intel-gpu-plugin=2025.0.0=hdc3f47d_3
  - libopenvino-intel-npu-plugin=2025.0.0=hdc3f47d_3
  - libopenvino-ir-frontend=2025.0.0=h981d57b_3
  - libopenvino-onnx-frontend=2025.0.0=h0e684df_3
  - libopenvino-paddle-frontend=2025.0.0=h0e684df_3
  - libopenvino-pytorch-frontend=2025.0.0=h5888daf_3
  - libopenvino-tensorflow-frontend=2025.0.0=h684f15b_3
  - libopenvino-tensorflow-lite-frontend=2025.0.0=h5888daf_3
  - libopus=1.5.2=hd0c01bc_0
  - libpciaccess=0.18=hb9d3cd8_0
  - libpng=1.6.47=h943b412_0
  - libprotobuf=5.29.3=h501fc15_1
  - librsvg=2.58.4=he92a37e_3
  - libsndfile=1.2.2=hc60ed4a_1
  - libsqlite=3.45.2=h2797004_0
  - libstdcxx=15.1.0=h8f9b012_2
  - libstdcxx-ng=15.1.0=h4852527_2
  - libsystemd0=257.4=h4e0b6ca_1
  - libtiff=4.7.0=hd9ff511_4
  - libudev1=257.4=hbe16f8c_1
  - libunwind=1.6.2=h9c3ff4c_0
  - liburing=2.9=h84d6215_0
  - libusb=1.0.28=h73b1eb8_1
  - libuuid=2.38.1=h0b41bf4_0
  - libva=2.22.0=h4f16b4b_2
  - libvorbis=1.3.7=h9c3ff4c_0
  - libvpx=1.14.1=hac33072_0
  - libwebp-base=1.5.0=h851e524_0
  - libxcb=1.17.0=h8a09558_0
  - libxcrypt=4.4.36=hd590300_1
  - libxkbcommon=1.9.2=h65c71a3_0
  - libxml2=2.13.8=h4bc477f_0
  - libzlib=1.3.1=hb9d3cd8_2
  - lz4-c=1.10.0=h5888daf_1
  - mpg123=1.32.9=hc50e24c_0
  - ncurses=6.4.20240210=h59595ed_0
  - ocl-icd=2.3.3=hb9d3cd8_0
  - opencl-headers=2024.10.24=h5888daf_0
  - openh264=2.6.0=hc22cd8d_0
  - openssl=3.5.0=h7b32b05_1
  - pango=1.56.3=h9ac818e_1
  - pcre2=10.45=hc749103_0
  - pip=25.0.1=pyh8b19718_0
  - pixman=0.46.0=h29eaf8c_0
  - pthread-stubs=0.4=hb9d3cd8_1002
  - pugixml=1.15=h3f63f65_0
  - pulseaudio-client=17.0=hac146a9_1
  - python=3.10.13=hd12c33a_1_cpython
  - readline=8.2=h8228510_1
  - sdl2=2.32.54=h3f2d84a_0
  - sdl3=3.2.12=he3e324a_2
  - setuptools=75.8.0=pyhff2d567_0
  - snappy=1.2.1=h8bd8927_1
  - sqlite=3.45.2=h2c6b66d_0
  - svt-av1=3.0.2=h5888daf_0
  - tbb=2022.1.0=h4ce085d_0
  - tk=8.6.13=noxft_hd72426e_102
  - wayland=1.23.1=h3e06ad9_1
  - wayland-protocols=1.43=hd8ed1ab_0
  - wheel=0.45.1=pyhd8ed1ab_1
  - x264=1!164.3095=h166bdaf_2
  - x265=3.5=h924138e_3
  - xkeyboard-config=2.44=hb9d3cd8_0
  - xorg-libice=1.1.2=hb9d3cd8_0
  - xorg-libsm=1.2.6=he73a12e_0
  - xorg-libx11=1.8.12=h4f16b4b_0
  - xorg-libxau=1.0.12=hb9d3cd8_0
  - xorg-libxcursor=1.2.3=hb9d3cd8_0
  - xorg-libxdmcp=1.1.5=hb9d3cd8_0
  - xorg-libxext=1.3.6=hb9d3cd8_0
  - xorg-libxfixes=6.0.1=hb9d3cd8_0
  - xorg-libxrender=0.9.12=hb9d3cd8_0
  - xorg-libxscrnsaver=1.2.4=hb9d3cd8_0
  - xz=5.8.1=hbcc6ac9_2
  - xz-gpl-tools=5.8.1=hbcc6ac9_2
  - xz-tools=5.8.1=hb9d3cd8_2
  - zlib=1.3.1=hb9d3cd8_2
  - zstd=1.5.7=hb8e6e7a_2
  - pip:
    - charset-normalizer==3.4.1
    - contourpy==1.3.1
    - cycler==0.12.1
    - dominate==2.9.1
    - einops==0.8.1
    - filelock==3.18.0
    - fonttools==4.57.0
    - fsspec==2025.3.0
    - idna==3.10
    - imageio==2.37.0
    - jinja2==3.1.6
    - joblib==1.4.2
    - jsonpatch==1.33
    - jsonpointer==3.0.0
    - kiwisolver==1.4.8
    - kornia==0.8.0
    - kornia-rs==0.1.8
    - lazy-loader==0.4
    - markupsafe==3.0.2
    - matplotlib==3.10.1
    - mpmath==1.3.0
    - networkx==3.4.2
    - numpy==2.2.4
    - nvidia-cublas-cu12==********
    - nvidia-cuda-cupti-cu12==12.4.127
    - nvidia-cuda-nvrtc-cu12==12.4.127
    - nvidia-cuda-runtime-cu12==12.4.127
    - nvidia-cudnn-cu12==********
    - nvidia-cufft-cu12==********
    - nvidia-curand-cu12==**********
    - nvidia-cusolver-cu12==********
    - nvidia-cusparse-cu12==**********
    - nvidia-cusparselt-cu12==0.6.2
    - nvidia-nccl-cu12==2.21.5
    - nvidia-nvjitlink-cu12==12.4.127
    - nvidia-nvtx-cu12==12.4.127
    - opencv-contrib-python==*********
    - opencv-python==*********
    - packaging==24.2
    - pandas==2.2.3
    - pillow==11.1.0
    - pyparsing==3.2.3
    - python-dateutil==2.9.0.post0
    - pytorch-msssim==1.0.0
    - pytz==2025.1
    - pywavelets==1.8.0
    - requests==2.32.3
    - scikit-image==0.25.2
    - scikit-learn==1.6.1
    - scipy==1.15.2
    - six==1.17.0
    - sympy==1.13.1
    - threadpoolctl==3.6.0
    - tifffile==2025.3.13
    - torch==2.6.0
    - torchvision==0.21.0
    - tornado==6.4.2
    - tqdm==4.67.1
    - triton==3.2.0
    - typing-extensions==4.12.2
    - tzdata==2025.1
    - urllib3==2.3.0
    - visdom==0.2.4
    - websocket-client==1.8.0
prefix: /data/ywj/miniconda/envs/python3.10
